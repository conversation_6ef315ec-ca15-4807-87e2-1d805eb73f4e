-- Test soubor pro minihru spojování kabelů
-- Spusťte tento soubor v FiveM konzoli nebo ho přidejte do svého resource

-- Základní test minihry
RegisterCommand('test_wiring', function(source, args)
    print("Spouštím test minihry spojování kabelů...")
    
    local success = exports['DVRP_minigame']:StartWiringMinigame({
        wire_count = 4,
        colors = "classic",
        snap_radius = 30,
        scale = 1.0
    }, {
        onComplete = function(playerId)
            print("✅ Minihra byla úspěšně dokončena!")
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                args = {'WIRING', 'Minihra dokončena úspěšně!'}
            })
        end,
        
        onFail = function(playerId, reason)
            print("❌ Minihra selhala: " .. (reason or "neznámý důvod"))
            TriggerEvent('chat:addMessage', {
                color = {255, 0, 0},
                args = {'WIRING', 'Minihra selhala: ' .. (reason or "neznámý důvod")}
            })
        end,
        
        onClose = function(playerId)
            print("🚪 Minihra byla zavřena")
            TriggerEvent('chat:addMessage', {
                color = {128, 128, 128},
                args = {'WIRING', 'Minihra byla zavřena'}
            })
        end
    })
    
    if success then
        print("✅ Minihra byla spuštěna úspěšně")
    else
        print("❌ Nepodařilo se spustit minihru")
    end
end)

-- Test s více kabely
RegisterCommand('test_wiring_hard', function(source, args)
    print("Spouštím těžkou verzi minihry...")
    
    exports['DVRP_minigame']:StartWiringMinigame({
        wire_count = 6,
        colors = "extended",
        snap_radius = 20,
        scale = 1.2
    })
end)

-- Test s rainbow barvami
RegisterCommand('test_wiring_rainbow', function(source, args)
    print("Spouštím rainbow verzi minihry...")
    
    exports['DVRP_minigame']:StartWiringMinigame({
        wire_count = 8,
        colors = "rainbow",
        snap_radius = 25,
        scale = 1.0
    })
end)

print("Test příkazy zaregistrovány:")
print("- /test_wiring - základní test")
print("- /test_wiring_hard - těžká verze")
print("- /test_wiring_rainbow - rainbow verze")
