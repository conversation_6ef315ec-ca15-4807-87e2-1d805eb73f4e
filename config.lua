Config = {}

-- ===========================
-- ZÁKLADNÍ NASTAVENÍ MINIHRY
-- ===========================

-- Poč<PERSON> kabelů (1-32)
Config.WireCount = 4

-- <PERSON><PERSON> kabelů
-- Možnosti: "auto", "classic", "extended", "rainbow" nebo vlastní pole barev
-- "auto" - automaticky vybere vhodnou paletu podle počtu kabelů
-- "classic" - klasické 4 barvy (modrá, červená, žlutá, fialová)
-- "extended" - rozšířená paleta pro více kabelů
-- "rainbow" - duha barev pro velké množství kabelů
-- Vlastní pole: {"#ff0000", "#00ff00", "#0000ff", ...}
Config.Colors = "auto"

-- Poloměr př<PERSON>ní (v pixelech) - jak bl<PERSON><PERSON><PERSON> mus<PERSON> být kabel k cíli
Config.SnapRadius = 25

-- ===========================
-- NASTAVENÍ ZOBRAZENÍ
-- ===========================

-- Velikost herního okna (v pixelech)
Config.GameSize = "907px"

-- Pozice na obrazovce (procenta)
Config.Position = {
    x = "50%",  -- horizontální pozice
    y = "50%"   -- vertikální pozice
}

-- Měřítko minihry (0.1 - 2.0)
Config.Scale = 1.0

-- ===========================
-- ZVUKOVÉ EFEKTY
-- ===========================

-- Povolit zvuky
Config.EnableSounds = true

-- Název zvukového souboru pro dokončení (v html/sound/)
Config.CompletionSound = "1.ogg"

-- Hlasitost zvuků (0.0 - 1.0)
Config.SoundVolume = 0.5

-- ===========================
-- HERNÍ MECHANIKY
-- ===========================

-- Čas na dokončení minihry (v sekundách, 0 = neomezeno)
Config.TimeLimit = 0

-- Automaticky zavřít po dokončení
Config.AutoCloseOnComplete = true

-- Zpoždění před zavřením po dokončení (v milisekundách)
Config.CloseDelay = 2000

-- Povolit ESC/Backspace pro zavření
Config.AllowEscapeClose = true

-- ===========================
-- OBTÍŽNOST
-- ===========================

-- Náhodné pořadí kabelů při každém spuštění
Config.RandomizeWires = true

-- Minimální vzdálenost mezi kabely (ovlivňuje obtížnost)
Config.MinWireDistance = 50

-- ===========================
-- CALLBACK FUNKCE
-- ===========================

-- Výchozí callback funkce (lze přepsat při volání exportu)
Config.DefaultCallbacks = {
    onComplete = function(playerId)
        print("Hráč " .. playerId .. " dokončil minihru spojování kabelů!")
    end,
    
    onFail = function(playerId, reason)
        print("Hráč " .. playerId .. " neuspěl v miniře: " .. (reason or "neznámý důvod"))
    end,
    
    onClose = function(playerId)
        print("Hráč " .. playerId .. " zavřel minihru")
    end
}

-- ===========================
-- POKROČILÉ NASTAVENÍ
-- ===========================

-- Debug režim (zobrazuje dodatečné informace v konzoli)
Config.Debug = true

-- Název resource (automaticky detekován)
Config.ResourceName = GetCurrentResourceName()

-- Maximální počet současně aktivních miniher na serveru
Config.MaxConcurrentGames = 50

-- ===========================
-- TEST COMMAND NASTAVENÍ
-- ===========================

-- Povolit test command
Config.EnableTestCommand = true

-- Název test commandu
Config.TestCommandName = "wiring_test"

-- Oprávnění potřebné pro test command (nil = všichni, "admin" = pouze admini)
Config.TestCommandPermission = nil

-- Výchozí nastavení pro test command
Config.TestCommandDefaults = {
    wire_count = 4,
    colors = "classic",
    snap_radius = 25,
    scale = 1.0
}

-- Zprávy pro test command
Config.TestCommandMessages = {
    started = "🔌 Test minihry spuštěn!",
    already_active = "⚠️ Minigra již běží!",
    no_permission = "❌ Nemáte oprávnění k použití tohoto příkazu!",
    completed = "✅ Test minigry dokončen úspěšně!",
    failed = "❌ Test minigry selhal!",
    closed = "🚪 Test minigry zavřen."
}

-- ===========================
-- PRESET KONFIGURACE
-- ===========================

-- Přednastavené konfigurace pro různé obtížnosti
Config.Presets = {
    easy = {
        WireCount = 3,
        Colors = "classic",
        SnapRadius = 35,
        TimeLimit = 0
    },
    
    normal = {
        WireCount = 4,
        Colors = "classic", 
        SnapRadius = 25,
        TimeLimit = 0
    },
    
    hard = {
        WireCount = 6,
        Colors = "extended",
        SnapRadius = 20,
        TimeLimit = 60
    },
    
    expert = {
        WireCount = 8,
        Colors = "extended",
        SnapRadius = 15,
        TimeLimit = 45
    },
    
    insane = {
        WireCount = 12,
        Colors = "rainbow",
        SnapRadius = 10,
        TimeLimit = 30
    }
}

-- ===========================
-- INTEGRACE S FRAMEWORKY
-- ===========================

-- ESX integrace
Config.UseESX = false
Config.ESXTriggerName = 'esx:getSharedObject'

-- QB-Core integrace  
Config.UseQBCore = false

-- Vlastní framework
Config.UseCustomFramework = false
Config.CustomFrameworkObject = nil
